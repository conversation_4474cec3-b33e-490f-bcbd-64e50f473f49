import 'package:flutter/foundation.dart';
import '../models/culture.dart';
import '../models/transfer_log.dart';
import '../database/database_helper.dart';

/// Provider for managing Culture data and state
/// Implements ChangeNotifier for reactive UI updates
class CultureProvider extends ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  List<Culture> _cultures = [];
  bool _isLoading = false;
  String? _error;

  /// Get all cultures
  List<Culture> get cultures => List.unmodifiable(_cultures);

  /// Get loading state
  bool get isLoading => _isLoading;

  /// Get error message
  String? get error => _error;

  /// Get cultures by status
  List<Culture> getCulturesByStatus(CultureStatus status) {
    return _cultures.where((culture) => culture.status == status).toList();
  }

  /// Get cultures by stage
  List<Culture> getCulturesByStage(CultureStage stage) {
    return _cultures.where((culture) => culture.currentStage == stage).toList();
  }

  /// Get active cultures count
  int get activeCulturesCount {
    return _cultures
        .where((culture) => culture.status == CultureStatus.active)
        .length;
  }

  /// Get cultures that need transfer (next transfer date is today or past)
  List<Culture> get culturesNeedingTransfer {
    final now = DateTime.now();
    return _cultures.where((culture) {
      if (culture.nextTransferDate == null) return false;
      return culture.nextTransferDate!.isBefore(now) ||
          _isSameDay(culture.nextTransferDate!, now);
    }).toList();
  }

  /// Load all cultures from database
  Future<void> loadCultures() async {
    _setLoading(true);
    _clearError();

    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableCultures,
        orderBy: 'created_at DESC',
      );

      _cultures = maps.map((map) => Culture.fromMap(map)).toList();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load cultures: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Add a new culture
  Future<Culture?> addCulture(Culture culture) async {
    _clearError();

    try {
      final db = await _databaseHelper.database;
      final id = await db.insert(DatabaseHelper.tableCultures, culture.toMap());

      final newCulture = culture.copyWith(id: id);
      _cultures.insert(
        0,
        newCulture,
      ); // Add to beginning for recent-first order
      notifyListeners();

      return newCulture;
    } catch (e) {
      _setError('Failed to add culture: $e');
      return null;
    }
  }

  /// Update an existing culture
  Future<bool> updateCulture(Culture culture) async {
    if (culture.id == null) return false;

    _clearError();

    try {
      final db = await _databaseHelper.database;
      final updatedCulture = culture.copyWith(updatedAt: DateTime.now());

      await db.update(
        DatabaseHelper.tableCultures,
        updatedCulture.toMap(),
        where: 'id = ?',
        whereArgs: [culture.id],
      );

      final index = _cultures.indexWhere((c) => c.id == culture.id);
      if (index != -1) {
        _cultures[index] = updatedCulture;
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('Failed to update culture: $e');
      return false;
    }
  }

  /// Delete a culture
  Future<bool> deleteCulture(int cultureId) async {
    _clearError();

    try {
      final db = await _databaseHelper.database;
      await db.delete(
        DatabaseHelper.tableCultures,
        where: 'id = ?',
        whereArgs: [cultureId],
      );

      _cultures.removeWhere((culture) => culture.id == cultureId);
      notifyListeners();

      return true;
    } catch (e) {
      _setError('Failed to delete culture: $e');
      return false;
    }
  }

  /// Get culture by ID
  Culture? getCultureById(int id) {
    try {
      return _cultures.firstWhere((culture) => culture.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Update culture status
  Future<bool> updateCultureStatus(int cultureId, CultureStatus status) async {
    final culture = getCultureById(cultureId);
    if (culture == null) return false;

    return await updateCulture(culture.copyWith(status: status));
  }

  /// Update culture stage and set next transfer date with transfer log
  Future<bool> transferCulture(
    int cultureId,
    CultureStage newStage, {
    DateTime? nextTransferDate,
    String? notes,
    bool? successIndicator,
  }) async {
    final culture = getCultureById(cultureId);
    if (culture == null) return false;

    _clearError();

    try {
      final db = await _databaseHelper.database;
      final transferDate = DateTime.now();

      // Create transfer log entry
      final transferLog = TransferLog(
        cultureId: cultureId,
        fromStage: culture.currentStage.value,
        toStage: newStage.value,
        transferDate: transferDate,
        notes: notes,
        successIndicator: successIndicator,
      );

      // Insert transfer log
      await db.insert(DatabaseHelper.tableTransferLogs, transferLog.toMap());

      // Update culture
      final updatedCulture = culture.copyWith(
        currentStage: newStage,
        lastTransferDate: transferDate,
        nextTransferDate: nextTransferDate,
        notes: notes ?? culture.notes,
      );

      return await updateCulture(updatedCulture);
    } catch (e) {
      _setError('Failed to transfer culture: $e');
      return false;
    }
  }

  /// Add photo to culture
  Future<bool> addPhotoToCulture(int cultureId, String photoPath) async {
    final culture = getCultureById(cultureId);
    if (culture == null) return false;

    final updatedPhotos = List<String>.from(culture.photos)..add(photoPath);
    return await updateCulture(culture.copyWith(photos: updatedPhotos));
  }

  /// Remove photo from culture
  Future<bool> removePhotoFromCulture(int cultureId, String photoPath) async {
    final culture = getCultureById(cultureId);
    if (culture == null) return false;

    final updatedPhotos = List<String>.from(culture.photos)..remove(photoPath);
    return await updateCulture(culture.copyWith(photos: updatedPhotos));
  }

  /// Add contamination event to culture
  Future<bool> addContaminationEvent(
    int cultureId,
    String cause, {
    String? notes,
  }) async {
    final culture = getCultureById(cultureId);
    if (culture == null) return false;

    final contaminationEvent = ContaminationEvent(
      date: DateTime.now(),
      cause: cause,
      notes: notes,
    );

    final updatedLog = List<ContaminationEvent>.from(culture.contaminationLog)
      ..add(contaminationEvent);

    return await updateCulture(
      culture.copyWith(
        contaminationLog: updatedLog,
        status: CultureStatus.contaminated,
      ),
    );
  }

  /// Get transfer logs for a specific culture
  Future<List<TransferLog>> getTransferLogs(int cultureId) async {
    _clearError();

    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        DatabaseHelper.tableTransferLogs,
        where: 'culture_id = ?',
        whereArgs: [cultureId],
        orderBy: 'transfer_date DESC',
      );

      return maps.map((map) => TransferLog.fromMap(map)).toList();
    } catch (e) {
      _setError('Failed to load transfer logs: $e');
      return [];
    }
  }

  /// Quick update culture status with optional transfer log
  Future<bool> quickUpdateStatus(
    int cultureId,
    CultureStatus newStatus, {
    String? notes,
  }) async {
    final culture = getCultureById(cultureId);
    if (culture == null) return false;

    _clearError();

    try {
      // If status is being changed to contaminated, add contamination event
      if (newStatus == CultureStatus.contaminated &&
          culture.status != CultureStatus.contaminated) {
        return await addContaminationEvent(
          cultureId,
          'Status changed to contaminated',
          notes: notes,
        );
      }

      // For other status changes, just update the culture
      final updatedCulture = culture.copyWith(
        status: newStatus,
        notes: notes ?? culture.notes,
      );

      return await updateCulture(updatedCulture);
    } catch (e) {
      _setError('Failed to update status: $e');
      return false;
    }
  }

  /// Clear all data (useful for testing)
  void clear() {
    _cultures.clear();
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Check if two dates are on the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
