import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/culture.dart';
import '../providers/culture_provider.dart';
import '../services/camera_service.dart';

class CultureDetailScreen extends StatefulWidget {
  final String cultureId;

  const CultureDetailScreen({super.key, required this.cultureId});

  @override
  State<CultureDetailScreen> createState() => _CultureDetailScreenState();
}

class _CultureDetailScreenState extends State<CultureDetailScreen> {
  final CameraService _cameraService = CameraService();
  Culture? _culture;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCulture();
  }

  Future<void> _loadCulture() async {
    final cultureProvider = Provider.of<CultureProvider>(
      context,
      listen: false,
    );
    await cultureProvider.loadCultures();

    final cultureId = int.tryParse(widget.cultureId);
    if (cultureId != null) {
      setState(() {
        _culture = cultureProvider.getCultureById(cultureId);
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _capturePhoto() async {
    // Check permissions first
    final hasPermissions = await _cameraService.checkAndRequestPermissions();
    if (!hasPermissions) {
      if (mounted) {
        _cameraService.showPermissionDeniedDialog(context);
      }
      return;
    }

    // Show photo source dialog
    final photo = await _cameraService.showPhotoSourceDialog(context);
    if (photo != null && _culture != null) {
      // Save photo to app directory
      final savedPath = await _cameraService.savePhoto(
        photo,
        _culture!.id.toString(),
      );
      if (savedPath != null) {
        // Add photo to culture
        final cultureProvider = Provider.of<CultureProvider>(
          context,
          listen: false,
        );
        final success = await cultureProvider.addPhotoToCulture(
          _culture!.id!,
          savedPath,
        );

        if (success) {
          // Reload culture data
          await _loadCulture();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Photo added successfully!')),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to add photo')),
            );
          }
        }
      }
    }
  }

  Future<void> _deletePhoto(String photoPath) async {
    if (_culture == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Photo'),
            content: const Text('Are you sure you want to delete this photo?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final cultureProvider = Provider.of<CultureProvider>(
        context,
        listen: false,
      );
      final success = await cultureProvider.removePhotoFromCulture(
        _culture!.id!,
        photoPath,
      );

      if (success) {
        // Delete the actual file
        await _cameraService.deletePhoto(photoPath);
        // Reload culture data
        await _loadCulture();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Photo deleted successfully!')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to delete photo')),
          );
        }
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Culture Details'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_culture == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Culture Details'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(child: Text('Culture not found')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Culture Details'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.camera_alt),
            onPressed: _capturePhoto,
            tooltip: 'Add Photo',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              // TODO: Navigate to edit culture screen
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Edit functionality coming soon!'),
                ),
              );
            },
            tooltip: 'Edit Culture',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Culture ID: ${_culture!.id}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Species: ${_culture!.species}'),
                    if (_culture!.variety != null)
                      Text('Variety: ${_culture!.variety}'),
                    if (_culture!.source != null)
                      Text('Source: ${_culture!.source}'),
                    Text('Status: ${_culture!.status.displayName}'),
                    Text('Stage: ${_culture!.currentStage.displayName}'),
                    Text('Created: ${_formatDate(_culture!.creationDate)}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Transfer Schedule',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Last Transfer: ${_culture!.lastTransferDate != null ? _formatDate(_culture!.lastTransferDate!) : 'N/A'}',
                    ),
                    Text(
                      'Next Transfer: ${_culture!.nextTransferDate != null ? _formatDate(_culture!.nextTransferDate!) : 'Not scheduled'}',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Photos',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${_culture!.photos.length} photo${_culture!.photos.length != 1 ? 's' : ''}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child:
                            _culture!.photos.isEmpty
                                ? const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.photo_camera,
                                        size: 64,
                                        color: Colors.grey,
                                      ),
                                      SizedBox(height: 16),
                                      Text(
                                        'No photos yet',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Tap the camera icon to add photos',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                                : GridView.builder(
                                  gridDelegate:
                                      const SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 2,
                                        crossAxisSpacing: 8,
                                        mainAxisSpacing: 8,
                                      ),
                                  itemCount: _culture!.photos.length,
                                  itemBuilder: (context, index) {
                                    final photoPath = _culture!.photos[index];
                                    return GestureDetector(
                                      onTap: () => _showPhotoDialog(photoPath),
                                      child: Stack(
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              image: DecorationImage(
                                                image: FileImage(
                                                  File(photoPath),
                                                ),
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            top: 4,
                                            right: 4,
                                            child: GestureDetector(
                                              onTap:
                                                  () => _deletePhoto(photoPath),
                                              child: Container(
                                                padding: const EdgeInsets.all(
                                                  4,
                                                ),
                                                decoration: const BoxDecoration(
                                                  color: Colors.red,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: const Icon(
                                                  Icons.delete,
                                                  color: Colors.white,
                                                  size: 16,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPhotoDialog(String photoPath) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppBar(
                  title: const Text('Photo'),
                  leading: IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        Navigator.of(context).pop();
                        _deletePhoto(photoPath);
                      },
                    ),
                  ],
                ),
                Expanded(
                  child: InteractiveViewer(
                    child: Image.file(File(photoPath), fit: BoxFit.contain),
                  ),
                ),
              ],
            ),
          ),
    );
  }
}
