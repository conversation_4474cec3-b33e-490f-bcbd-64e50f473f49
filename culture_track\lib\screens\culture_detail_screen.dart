import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/culture.dart';
import '../providers/culture_provider.dart';
import '../services/camera_service.dart';

class CultureDetailScreen extends StatefulWidget {
  final String cultureId;

  const CultureDetailScreen({super.key, required this.cultureId});

  @override
  State<CultureDetailScreen> createState() => _CultureDetailScreenState();
}

class _CultureDetailScreenState extends State<CultureDetailScreen> {
  final CameraService _cameraService = CameraService();
  Culture? _culture;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCulture();
  }

  Future<void> _loadCulture() async {
    final cultureProvider = Provider.of<CultureProvider>(
      context,
      listen: false,
    );
    await cultureProvider.loadCultures();

    final cultureId = int.tryParse(widget.cultureId);
    if (cultureId != null) {
      setState(() {
        _culture = cultureProvider.getCultureById(cultureId);
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _capturePhoto() async {
    // Check permissions first
    final hasPermissions = await _cameraService.checkAndRequestPermissions();
    if (!hasPermissions) {
      if (mounted) {
        _cameraService.showPermissionDeniedDialog(context);
      }
      return;
    }

    // Show photo source dialog
    if (!mounted) return;
    final photo = await _cameraService.showPhotoSourceDialog(context);
    if (photo != null && _culture != null && mounted) {
      // Save photo to app directory
      final savedPath = await _cameraService.savePhoto(
        photo,
        _culture!.id.toString(),
      );
      if (savedPath != null && mounted) {
        // Add photo to culture
        final cultureProvider = Provider.of<CultureProvider>(
          context,
          listen: false,
        );
        final success = await cultureProvider.addPhotoToCulture(
          _culture!.id!,
          savedPath,
        );

        if (success) {
          // Reload culture data
          await _loadCulture();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Photo added successfully!')),
            );
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to add photo')),
            );
          }
        }
      }
    }
  }

  Future<void> _deletePhoto(String photoPath) async {
    if (_culture == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Photo'),
            content: const Text('Are you sure you want to delete this photo?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true && mounted) {
      final cultureProvider = Provider.of<CultureProvider>(
        context,
        listen: false,
      );
      final success = await cultureProvider.removePhotoFromCulture(
        _culture!.id!,
        photoPath,
      );

      if (success) {
        // Delete the actual file
        await _cameraService.deletePhoto(photoPath);
        // Reload culture data
        await _loadCulture();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Photo deleted successfully!')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to delete photo')),
          );
        }
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Culture Details'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_culture == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Culture Details'),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        ),
        body: const Center(child: Text('Culture not found')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Culture Details'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.camera_alt),
            onPressed: _capturePhoto,
            tooltip: 'Add Photo',
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _showEditDialog,
            tooltip: 'Edit Culture',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _showDeleteConfirmation();
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete Culture'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Culture ID: ${_culture!.id}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Species: ${_culture!.species}'),
                    if (_culture!.variety != null)
                      Text('Variety: ${_culture!.variety}'),
                    if (_culture!.source != null)
                      Text('Source: ${_culture!.source}'),
                    Text('Status: ${_culture!.status.displayName}'),
                    Text('Stage: ${_culture!.currentStage.displayName}'),
                    Text('Created: ${_formatDate(_culture!.creationDate)}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Transfer Schedule',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Last Transfer: ${_culture!.lastTransferDate != null ? _formatDate(_culture!.lastTransferDate!) : 'N/A'}',
                    ),
                    Text(
                      'Next Transfer: ${_culture!.nextTransferDate != null ? _formatDate(_culture!.nextTransferDate!) : 'Not scheduled'}',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Quick Actions',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _showQuickStageUpdate,
                            icon: const Icon(Icons.trending_up, size: 18),
                            label: const Text('Transfer Stage'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _showQuickStatusUpdate,
                            icon: const Icon(Icons.update, size: 18),
                            label: const Text('Update Status'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Photos',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${_culture!.photos.length} photo${_culture!.photos.length != 1 ? 's' : ''}',
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child:
                            _culture!.photos.isEmpty
                                ? const Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.photo_camera,
                                        size: 64,
                                        color: Colors.grey,
                                      ),
                                      SizedBox(height: 16),
                                      Text(
                                        'No photos yet',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Tap the camera icon to add photos',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                                : GridView.builder(
                                  gridDelegate:
                                      const SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 2,
                                        crossAxisSpacing: 8,
                                        mainAxisSpacing: 8,
                                      ),
                                  itemCount: _culture!.photos.length,
                                  itemBuilder: (context, index) {
                                    final photoPath = _culture!.photos[index];
                                    return GestureDetector(
                                      onTap: () => _showPhotoDialog(photoPath),
                                      child: Stack(
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                              image: DecorationImage(
                                                image: FileImage(
                                                  File(photoPath),
                                                ),
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                          ),
                                          Positioned(
                                            top: 4,
                                            right: 4,
                                            child: GestureDetector(
                                              onTap:
                                                  () => _deletePhoto(photoPath),
                                              child: Container(
                                                padding: const EdgeInsets.all(
                                                  4,
                                                ),
                                                decoration: const BoxDecoration(
                                                  color: Colors.red,
                                                  shape: BoxShape.circle,
                                                ),
                                                child: const Icon(
                                                  Icons.delete,
                                                  color: Colors.white,
                                                  size: 16,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPhotoDialog(String photoPath) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AppBar(
                  title: const Text('Photo'),
                  leading: IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        Navigator.of(context).pop();
                        _deletePhoto(photoPath);
                      },
                    ),
                  ],
                ),
                Expanded(
                  child: InteractiveViewer(
                    child: Image.file(File(photoPath), fit: BoxFit.contain),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  /// Show edit culture dialog
  void _showEditDialog() {
    if (_culture == null) return;

    showDialog(
      context: context,
      builder: (context) => _EditCultureDialog(culture: _culture!),
    ).then((updatedCulture) {
      if (updatedCulture != null) {
        setState(() {
          _culture = updatedCulture;
        });
      }
    });
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation() {
    if (_culture == null) return;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Culture'),
            content: Text(
              'Are you sure you want to delete "${_culture!.species}"? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _deleteCulture();
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  /// Delete the culture
  Future<void> _deleteCulture() async {
    if (_culture?.id == null) return;

    final cultureProvider = Provider.of<CultureProvider>(
      context,
      listen: false,
    );

    final success = await cultureProvider.deleteCulture(_culture!.id!);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Culture deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(); // Go back to culture list
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(cultureProvider.error ?? 'Failed to delete culture'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show quick stage update dialog
  void _showQuickStageUpdate() {
    if (_culture == null) return;

    showDialog(
      context: context,
      builder: (context) => _QuickStageUpdateDialog(culture: _culture!),
    ).then((updatedCulture) {
      if (updatedCulture != null) {
        setState(() {
          _culture = updatedCulture;
        });
      }
    });
  }

  /// Show quick status update dialog
  void _showQuickStatusUpdate() {
    if (_culture == null) return;

    showDialog(
      context: context,
      builder: (context) => _QuickStatusUpdateDialog(culture: _culture!),
    ).then((updatedCulture) {
      if (updatedCulture != null) {
        setState(() {
          _culture = updatedCulture;
        });
      }
    });
  }
}

/// Dialog for editing culture details
class _EditCultureDialog extends StatefulWidget {
  final Culture culture;

  const _EditCultureDialog({required this.culture});

  @override
  State<_EditCultureDialog> createState() => _EditCultureDialogState();
}

class _EditCultureDialogState extends State<_EditCultureDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _speciesController;
  late TextEditingController _varietyController;
  late TextEditingController _sourceController;
  late TextEditingController _notesController;
  late CultureStage _selectedStage;
  late CultureStatus _selectedStatus;
  DateTime? _nextTransferDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _speciesController = TextEditingController(text: widget.culture.species);
    _varietyController = TextEditingController(
      text: widget.culture.variety ?? '',
    );
    _sourceController = TextEditingController(
      text: widget.culture.source ?? '',
    );
    _notesController = TextEditingController(text: widget.culture.notes ?? '');
    _selectedStage = widget.culture.currentStage;
    _selectedStatus = widget.culture.status;
    _nextTransferDate = widget.culture.nextTransferDate;
  }

  @override
  void dispose() {
    _speciesController.dispose();
    _varietyController.dispose();
    _sourceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Culture'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _speciesController,
                  decoration: const InputDecoration(
                    labelText: 'Species *',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Species is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _varietyController,
                  decoration: const InputDecoration(
                    labelText: 'Variety',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _sourceController,
                  decoration: const InputDecoration(
                    labelText: 'Source',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<CultureStage>(
                  value: _selectedStage,
                  decoration: const InputDecoration(
                    labelText: 'Current Stage',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      CultureStage.values.map((stage) {
                        return DropdownMenuItem(
                          value: stage,
                          child: Text(stage.displayName),
                        );
                      }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedStage = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<CultureStatus>(
                  value: _selectedStatus,
                  decoration: const InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      CultureStatus.values.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Text(status.displayName),
                        );
                      }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedStatus = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                InkWell(
                  onTap: _selectNextTransferDate,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Next Transfer Date',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_today),
                    ),
                    child: Text(
                      _nextTransferDate != null
                          ? _formatDate(_nextTransferDate!)
                          : 'Not set',
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'Notes',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveChanges,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('Save'),
        ),
      ],
    );
  }

  Future<void> _selectNextTransferDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _nextTransferDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _nextTransferDate = date;
      });
    }
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    final updatedCulture = widget.culture.copyWith(
      species: _speciesController.text.trim(),
      variety:
          _varietyController.text.trim().isEmpty
              ? null
              : _varietyController.text.trim(),
      source:
          _sourceController.text.trim().isEmpty
              ? null
              : _sourceController.text.trim(),
      currentStage: _selectedStage,
      status: _selectedStatus,
      nextTransferDate: _nextTransferDate,
      notes:
          _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
      updatedAt: DateTime.now(),
    );

    final cultureProvider = Provider.of<CultureProvider>(
      context,
      listen: false,
    );

    final success = await cultureProvider.updateCulture(updatedCulture);

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        Navigator.of(context).pop(updatedCulture);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Culture updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(cultureProvider.error ?? 'Failed to update culture'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Dialog for quick stage updates (transfers)
class _QuickStageUpdateDialog extends StatefulWidget {
  final Culture culture;

  const _QuickStageUpdateDialog({required this.culture});

  @override
  State<_QuickStageUpdateDialog> createState() =>
      _QuickStageUpdateDialogState();
}

class _QuickStageUpdateDialogState extends State<_QuickStageUpdateDialog> {
  late CultureStage _selectedStage;
  DateTime? _nextTransferDate;
  final _notesController = TextEditingController();
  bool? _successIndicator;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedStage = _getNextStage(widget.culture.currentStage);
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  CultureStage _getNextStage(CultureStage currentStage) {
    switch (currentStage) {
      case CultureStage.initiation:
        return CultureStage.multiplication;
      case CultureStage.multiplication:
        return CultureStage.rooting;
      case CultureStage.rooting:
        return CultureStage.acclimatization;
      case CultureStage.acclimatization:
        return CultureStage.acclimatization; // Stay at final stage
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Transfer Culture'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Current Stage: ${widget.culture.currentStage.displayName}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<CultureStage>(
              value: _selectedStage,
              decoration: const InputDecoration(
                labelText: 'Transfer to Stage',
                border: OutlineInputBorder(),
              ),
              items:
                  CultureStage.values.map((stage) {
                    return DropdownMenuItem(
                      value: stage,
                      child: Text(stage.displayName),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedStage = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: _selectNextTransferDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'Next Transfer Date (Optional)',
                  border: OutlineInputBorder(),
                  suffixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _nextTransferDate != null
                      ? _formatDate(_nextTransferDate!)
                      : 'Not set',
                ),
              ),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<bool?>(
              value: _successIndicator,
              decoration: const InputDecoration(
                labelText: 'Transfer Success',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: null, child: Text('Pending')),
                DropdownMenuItem(value: true, child: Text('Success')),
                DropdownMenuItem(value: false, child: Text('Failed')),
              ],
              onChanged: (value) {
                setState(() {
                  _successIndicator = value;
                });
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Transfer Notes (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _performTransfer,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('Transfer'),
        ),
      ],
    );
  }

  Future<void> _selectNextTransferDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate:
          _nextTransferDate ?? DateTime.now().add(const Duration(days: 14)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _nextTransferDate = date;
      });
    }
  }

  Future<void> _performTransfer() async {
    setState(() {
      _isLoading = true;
    });

    final cultureProvider = Provider.of<CultureProvider>(
      context,
      listen: false,
    );

    final success = await cultureProvider.transferCulture(
      widget.culture.id!,
      _selectedStage,
      nextTransferDate: _nextTransferDate,
      notes:
          _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
      successIndicator: _successIndicator,
    );

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        final updatedCulture = cultureProvider.getCultureById(
          widget.culture.id!,
        );
        Navigator.of(context).pop(updatedCulture);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Culture transferred successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              cultureProvider.error ?? 'Failed to transfer culture',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Dialog for quick status updates
class _QuickStatusUpdateDialog extends StatefulWidget {
  final Culture culture;

  const _QuickStatusUpdateDialog({required this.culture});

  @override
  State<_QuickStatusUpdateDialog> createState() =>
      _QuickStatusUpdateDialogState();
}

class _QuickStatusUpdateDialogState extends State<_QuickStatusUpdateDialog> {
  late CultureStatus _selectedStatus;
  final _notesController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.culture.status;
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Status'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Current Status: ${widget.culture.status.displayName}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<CultureStatus>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'New Status',
                border: OutlineInputBorder(),
              ),
              items:
                  CultureStatus.values.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Row(
                        children: [
                          Icon(
                            _getStatusIcon(status),
                            color: _getStatusColor(status),
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(status.displayName),
                        ],
                      ),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedStatus = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Status Update Notes (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateStatus,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('Update'),
        ),
      ],
    );
  }

  IconData _getStatusIcon(CultureStatus status) {
    switch (status) {
      case CultureStatus.active:
        return Icons.check_circle;
      case CultureStatus.contaminated:
        return Icons.warning;
      case CultureStatus.transferred:
        return Icons.swap_horiz;
      case CultureStatus.completed:
        return Icons.done_all;
      case CultureStatus.failed:
        return Icons.error;
    }
  }

  Color _getStatusColor(CultureStatus status) {
    switch (status) {
      case CultureStatus.active:
        return Colors.green;
      case CultureStatus.contaminated:
        return Colors.red;
      case CultureStatus.transferred:
        return Colors.blue;
      case CultureStatus.completed:
        return Colors.purple;
      case CultureStatus.failed:
        return Colors.grey;
    }
  }

  Future<void> _updateStatus() async {
    setState(() {
      _isLoading = true;
    });

    final cultureProvider = Provider.of<CultureProvider>(
      context,
      listen: false,
    );

    final success = await cultureProvider.quickUpdateStatus(
      widget.culture.id!,
      _selectedStatus,
      notes:
          _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
    );

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      if (success) {
        final updatedCulture = cultureProvider.getCultureById(
          widget.culture.id!,
        );
        Navigator.of(context).pop(updatedCulture);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Status updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(cultureProvider.error ?? 'Failed to update status'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
